'use client'

import { X, Upload, Package, Trash2 } from 'lucide-react'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import { useState, useEffect } from 'react'

import { Product, PRODUCT_CATEGORIES } from '@/lib/supabase'

interface ProductModalProps {
  isOpen: boolean
  onClose: () => void
  product?: Product | null
}

export default function ProductModal({ isOpen, onClose, product }: ProductModalProps) {
  const { resolvedTheme } = useTheme()
  const [formData, setFormData] = useState({
    name: '',
    net_weight: '',
    price: '',
    retail_price: '',
    stock_quantity: '',
    category: '',
    image_url: '',
    image_public_id: ''
  })
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)

  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name,
        net_weight: product.net_weight,
        price: product.price.toString(),
        retail_price: product.retail_price?.toString() || '',
        stock_quantity: product.stock_quantity.toString(),
        category: product.category,
        image_url: product.image_url || '',
        image_public_id: product.image_public_id || ''
      })
      setImagePreview(product.image_url || '')
    } else {
      setFormData({
        name: '',
        net_weight: '',
        price: '',
        retail_price: '',
        stock_quantity: '',
        category: '',
        image_url: '',
        image_public_id: ''
      })
      setImagePreview('')
    }
    setImageFile(null)
  }, [product, isOpen])

  // Add beforeunload event listener to detect unexpected page refreshes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (loading || uploading) {
        console.warn('⚠️ ProductModal: Page refresh detected during form submission!')
        e.preventDefault()
        e.returnValue = 'Are you sure you want to leave? Your changes may not be saved.'
        return 'Are you sure you want to leave? Your changes may not be saved.'
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => window.removeEventListener('beforeunload', handleBeforeUnload)
  }, [loading, uploading])

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setImageFile(file)
      const reader = new FileReader()
      reader.onloadend = () => {
        setImagePreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleRemoveImage = async () => {
    // Delete from Cloudinary if there's a public_id
    if (formData.image_public_id) {
      try {
        const response = await fetch(`/api/upload?public_id=${encodeURIComponent(formData.image_public_id)}`, {
          method: 'DELETE'
        })

        if (response.ok) {
          console.log(`Successfully deleted product image: ${formData.image_public_id}`)
        } else {
          console.error('Failed to delete product image from Cloudinary')
        }
      } catch (error) {
        console.error('Error deleting product image:', error)
      }
    }

    // Clear the image data
    setFormData(prev => ({
      ...prev,
      image_url: '',
      image_public_id: ''
    }))
    setImagePreview('')
    setImageFile(null)
  }

  const uploadImage = async (): Promise<{ url: string; public_id: string }> => {
    // If no new image file, return existing image data
    if (!imageFile) {
      return {
        url: formData.image_url || '',
        public_id: formData.image_public_id || ''
      }
    }

    setUploading(true)
    try {
      const uploadFormData = new FormData()
      uploadFormData.append('file', imageFile)

      // Include old public_id for automatic cleanup
      if (formData.image_public_id) {
        uploadFormData.append('old_public_id', formData.image_public_id)
      }

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: uploadFormData,
      })

      if (!response.ok) {
        const errorText = await response.text()
        throw new Error(`Upload failed: ${response.status} ${response.statusText} - ${errorText}`)
      }

      const data = await response.json()

      if (!data.url || !data.public_id) {
        throw new Error('Invalid upload response: missing url or public_id')
      }

      return { url: data.url, public_id: data.public_id }
    } catch (error) {
      console.error('Error uploading image:', error)
      // Re-throw with more context
      throw new Error(`Image upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setUploading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    console.log('🔄 ProductModal: Form submission started')

    // CRITICAL: Prevent default form submission behavior to avoid page refresh
    e.preventDefault()
    e.stopPropagation()

    // Prevent multiple submissions
    if (loading || uploading) {
      console.log('⚠️ ProductModal: Submission blocked - already loading')
      return
    }

    setLoading(true)
    console.log('🔄 ProductModal: Loading state set to true')

    try {
      // Upload image if there's a new one - with better error handling
      let imageData
      try {
        console.log('🔄 ProductModal: Starting image upload')
        imageData = await uploadImage()
        console.log('✅ ProductModal: Image upload successful', imageData)
      } catch (uploadError) {
        console.error('❌ ProductModal: Image upload failed:', uploadError)

        // Provide more specific error messages
        let errorMessage = 'Failed to upload image. Please try again.'
        if (uploadError instanceof Error) {
          if (uploadError.message.includes('configuration error')) {
            errorMessage = 'Server configuration error. Please contact support.'
          } else if (uploadError.message.includes('file type')) {
            errorMessage = 'Invalid file type. Please use JPEG, PNG, or WebP images.'
          } else if (uploadError.message.includes('file size')) {
            errorMessage = 'File too large. Please use an image smaller than 5MB.'
          } else if (uploadError.message.includes('500')) {
            errorMessage = 'Server error during upload. Please check your internet connection and try again.'
          }
        }

        alert(errorMessage)
        return
      }

      const productData = {
        ...formData,
        image_url: imageData.url,
        image_public_id: imageData.public_id,
        price: parseFloat(formData.price),
        retail_price: formData.retail_price ? parseFloat(formData.retail_price) : null,
        stock_quantity: parseInt(formData.stock_quantity),
        // Include old image public_id for cleanup during update
        ...(product && { old_image_public_id: product.image_public_id })
      }

      const url = product ? `/api/products/${product.id}` : '/api/products'
      const method = product ? 'PUT' : 'POST'

      console.log(`🔄 ProductModal: Sending ${method} request to ${url}`)

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      })

      console.log('📡 ProductModal: API response received', response.status, response.statusText)

      if (response.ok) {
        console.log('✅ ProductModal: Product saved successfully, closing modal')
        // Success: Close modal and let parent handle refresh
        onClose()
        // The parent component (ProductsSection) will handle refreshing the products list
        // through the handleModalClose callback which calls fetchProducts() and onStatsUpdate()
      } else {
        const errorData = await response.json()
        console.error('❌ ProductModal: API error:', errorData)
        alert(errorData.error || 'Failed to save product')
      }
    } catch (error) {
      console.error('❌ ProductModal: Unexpected error:', error)
      alert('Failed to save product. Please try again.')
    } finally {
      setLoading(false)
      console.log('🔄 ProductModal: Loading state set to false')
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div
        className="rounded-xl p-6 w-full max-w-md max-h-[90vh] overflow-y-auto shadow-2xl"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
        }}
      >
        <div className="flex justify-between items-center mb-4">
          <h2
            className="text-xl font-semibold"
            style={{
              color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
            }}
          >
            {product ? 'Edit Product in List' : 'Add Product to List'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 rounded-lg text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form
          onSubmit={handleSubmit}
          className="space-y-4"
          noValidate
          autoComplete="off"
          onKeyDown={(e) => {
            // Prevent Enter key from submitting form unless it's the submit button
            if (e.key === 'Enter' && e.target !== e.currentTarget) {
              const target = e.target as HTMLElement
              if (target.tagName !== 'BUTTON' || target.getAttribute('type') !== 'submit') {
                e.preventDefault()
              }
            }
          }}
        >
          {/* Image Upload */}
          <div>
            <label
              className="block text-sm font-medium mb-2"
              style={{
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
              }}
            >
              Product Image
            </label>
            <div className="flex flex-col items-center">
              <div
                className="w-32 h-32 border-2 border-dashed rounded-lg flex items-center justify-center mb-2"
                style={{
                  borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db'
                }}
              >
                {imagePreview ? (
                  <Image
                    src={imagePreview}
                    alt="Preview"
                    width={128}
                    height={128}
                    className="w-full h-full object-cover rounded-lg"
                  />
                ) : (
                  <Package
                    className="h-12 w-12"
                    style={{
                      color: resolvedTheme === 'dark' ? '#94a3b8' : '#9ca3af'
                    }}
                  />
                )}
              </div>
              <input
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                className="hidden"
                id="image-upload"
              />
              <div className="flex gap-2">
                <label
                  htmlFor="image-upload"
                  className="flex items-center px-3 py-2 border rounded-md cursor-pointer transition-colors"
                  style={{
                    borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                    backgroundColor: resolvedTheme === 'dark' ? 'transparent' : '#ffffff',
                    color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#334155' : '#f9fafb'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? 'transparent' : '#ffffff'
                  }}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Choose Image
                </label>
                {(imagePreview || formData.image_url) && (
                  <button
                    type="button"
                    onClick={handleRemoveImage}
                    className="flex items-center px-3 py-2 border rounded-md cursor-pointer transition-colors text-red-600 hover:text-red-700 border-red-300 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:border-red-600 dark:hover:bg-red-900/20"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Remove
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Product Name */}
          <div>
            <label
              className="block text-sm font-medium mb-1"
              style={{
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
              }}
            >
              Product Name *
            </label>
            <input
              id="product-name"
              name="name"
              type="text"
              required
              placeholder="Enter product name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors placeholder:text-gray-400 dark:placeholder:text-gray-500"
              style={{
                borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
              }}
              autoComplete="off"
            />
          </div>

          {/* Net Weight */}
          <div>
            <label
              className="block text-sm font-medium mb-1"
              style={{
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
              }}
            >
              Net Weight *
            </label>
            <input
              id="product-net-weight"
              name="net_weight"
              type="text"
              required
              placeholder="e.g., 100g, 1L, 250ml"
              value={formData.net_weight}
              onChange={(e) => setFormData({ ...formData, net_weight: e.target.value })}
              className="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors placeholder:text-gray-400 dark:placeholder:text-gray-500"
              style={{
                borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
              }}
              autoComplete="off"
            />
          </div>

          {/* Unit Price */}
          <div>
            <label
              htmlFor="product-price"
              className="block text-sm font-medium mb-1"
              style={{
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
              }}
            >
              Unit Price (₱) *
            </label>
            <input
              id="product-price"
              name="price"
              type="number"
              step="0.01"
              min="0"
              required
              placeholder="0.00"
              value={formData.price}
              onChange={(e) => setFormData({ ...formData, price: e.target.value })}
              className="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors placeholder:text-gray-400 dark:placeholder:text-gray-500"
              style={{
                borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
              }}
              autoComplete="off"
            />
          </div>

          {/* Retail Price */}
          <div>
            <label
              htmlFor="product-retail-price"
              className="block text-sm font-medium mb-1"
              style={{
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
              }}
            >
              Retail Price (₱)
            </label>
            <input
              id="product-retail-price"
              name="retail_price"
              type="number"
              step="0.01"
              min="0"
              placeholder="0.00"
              value={formData.retail_price}
              onChange={(e) => setFormData({ ...formData, retail_price: e.target.value })}
              className="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors placeholder:text-gray-400 dark:placeholder:text-gray-500"
              style={{
                borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
              }}
              autoComplete="off"
            />
            <p className="text-xs mt-1" style={{ color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280' }}>
              Optional: Selling price to customers (if different from unit price)
            </p>
          </div>

          {/* Stock Quantity */}
          <div>
            <label
              htmlFor="product-stock-quantity"
              className="block text-sm font-medium mb-1"
              style={{
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
              }}
            >
              Stock Quantity *
            </label>
            <input
              id="product-stock-quantity"
              name="stock_quantity"
              type="number"
              min="0"
              required
              placeholder="0"
              value={formData.stock_quantity}
              onChange={(e) => setFormData({ ...formData, stock_quantity: e.target.value })}
              className="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors placeholder:text-gray-400 dark:placeholder:text-gray-500"
              style={{
                borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
              }}
              autoComplete="off"
            />
          </div>

          {/* Category */}
          <div>
            <label
              className="block text-sm font-medium mb-1"
              style={{
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
              }}
            >
              Category *
            </label>
            <select
              id="product-category"
              name="category"
              required
              value={formData.category}
              onChange={(e) => setFormData({ ...formData, category: e.target.value })}
              className="w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors"
              style={{
                borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
              }}
              autoComplete="off"
            >
              <option value="" style={{
                backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                color: resolvedTheme === 'dark' ? '#94a3b8' : '#6b7280'
              }}>
                Select Category
              </option>
              {PRODUCT_CATEGORIES.map(category => (
                <option
                  key={category}
                  value={category}
                  style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#ffffff',
                    color: resolvedTheme === 'dark' ? '#f1f5f9' : '#111827'
                  }}
                >
                  {category}
                </option>
              ))}
            </select>
          </div>

          {/* Submit Button */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border rounded-md transition-colors"
              style={{
                borderColor: resolvedTheme === 'dark' ? '#475569' : '#d1d5db',
                backgroundColor: resolvedTheme === 'dark' ? 'transparent' : '#ffffff',
                color: resolvedTheme === 'dark' ? '#f1f5f9' : '#374151'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#334155' : '#f9fafb'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? 'transparent' : '#ffffff'
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading || uploading}
              className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 transition-colors"
              onClick={(e) => {
                // Additional safeguard to prevent any default behavior
                if (loading || uploading) {
                  e.preventDefault()
                  e.stopPropagation()
                }
              }}
            >
              {loading || uploading ? 'Saving...' : (product ? 'Update in List' : 'Add to List')}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
